import GOOGLE_AUTH_CONFIG from "../config/google.auth.config";
import AuthService from "./auth.service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as WebBrowser from "expo-web-browser";
import API_CONFIG from "../config/api.config";

// Conditional import for Google Sign-In
let GoogleSignin: any = null;
let statusCodes: any = null;

try {
  const googleSignInModule = require("@react-native-google-signin/google-signin");
  GoogleSignin = googleSignInModule.GoogleSignin;
  statusCodes = googleSignInModule.statusCodes;
  console.log("Google Sign-In native module loaded successfully");
} catch (error) {
  console.log("Google Sign-In: Using web authentication (normal in Expo Go)");
}

class GoogleAuthService {
  private static isConfigured = false;

  // Configure Google Sign-In
  static configure() {
    if (this.isConfigured || !GoogleSignin) return;

    GoogleSignin.configure({
      webClientId: GOOGLE_AUTH_CONFIG.WEB_CLIENT_ID,
      offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
      hostedDomain: "", // specifies a hosted domain restriction
      forceCodeForRefreshToken: true, // [Android] related to `serverAuthCode`, read the docs link below *.
      accountName: "", // [Android] specifies an account name on the device that should be used
      iosClientId: GOOGLE_AUTH_CONFIG.IOS_CLIENT_ID, // [iOS] if you want to specify the client ID of type iOS (otherwise, it is taken from GoogleService-Info.plist)
      googleServicePlistPath: "", // [iOS] if you renamed your GoogleService-Info.plist you'll need to add this line.
      openIdOfflineAccess: false, // [iOS] Use OpenID Connect instead of Google+ API
      profileImageSize: 120, // [iOS] The desired height (and width) of the profile image. Defaults to 120px
    });

    this.isConfigured = true;
  }

  // Check if Google Play Services are available
  static async checkPlayServices() {
    if (!GoogleSignin) return false;

    try {
      await GoogleSignin.hasPlayServices();
      return true;
    } catch (error) {
      console.error("Google Play Services not available:", error);
      return false;
    }
  }

  // Sign in with Google
  static async signIn() {
    try {
      // Check if native module is available
      if (!GoogleSignin) {
        console.log("Using web-based Google Sign-In fallback");
        return await this.signInWithWebBrowser();
      }

      // Configure if not already done
      this.configure();

      // Check if Google Play Services are available
      const hasPlayServices = await this.checkPlayServices();
      if (!hasPlayServices) {
        console.log("Google Play Services not available, falling back to web");
        return await this.signInWithWebBrowser();
      }

      // Check if user is already signed in
      const isSignedIn = await GoogleSignin.isSignedIn();
      if (isSignedIn) {
        // Get current user info
        const userInfo = await GoogleSignin.getCurrentUser();
        return userInfo;
      }

      // Sign in
      const userInfo = await GoogleSignin.signIn();
      console.log("Google Sign-In successful:", userInfo);

      // Get the ID token for backend authentication
      const tokens = await GoogleSignin.getTokens();
      console.log("Google tokens:", tokens);

      // Send the ID token to your backend for verification
      const authResponse = await AuthService.authenticateWithGoogle({
        idToken: tokens.idToken,
        accessToken: tokens.accessToken,
      });

      return {
        userInfo,
        authResponse,
        tokens,
      };
    } catch (error: any) {
      console.error("Google Sign-In error:", error);

      if (statusCodes && error.code === statusCodes.SIGN_IN_CANCELLED) {
        throw new Error("Sign in was cancelled");
      } else if (statusCodes && error.code === statusCodes.IN_PROGRESS) {
        throw new Error("Sign in is already in progress");
      } else if (statusCodes && error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log("Google Play Services not available, falling back to web");
        return await this.signInWithWebBrowser();
      } else {
        throw new Error("Google Sign-In failed: " + error.message);
      }
    }
  }

  // Fallback web-based sign-in for Expo Go
  static async signInWithWebBrowser() {
    try {
      console.log("Using web browser for Google Sign-In");

      const result = await WebBrowser.openAuthSessionAsync(
        `https://accounts.google.com/o/oauth2/v2/auth?` +
          `client_id=${GOOGLE_AUTH_CONFIG.CLIENT_ID}` +
          `&redirect_uri=${encodeURIComponent(API_CONFIG.BASE_URL + "/api/Account/google-callback")}` +
          `&response_type=code` +
          `&scope=${encodeURIComponent("profile email")}`,
        API_CONFIG.BASE_URL
      );

      if (result.type === "success" && result.url) {
        // Extract authorization code from URL
        const url = new URL(result.url);
        const code = url.searchParams.get("code");

        if (code) {
          // Send code to backend for token exchange
          const authResponse = await AuthService.authenticateWithGoogle({ code });

          return {
            userInfo: null, // Web flow doesn't provide user info directly
            authResponse,
            tokens: null,
          };
        }
      }

      throw new Error("Google Sign-In was cancelled or failed");
    } catch (error: any) {
      console.error("Web Google Sign-In error:", error);
      throw error;
    }
  }

  // Sign out
  static async signOut() {
    try {
      if (GoogleSignin) {
        await GoogleSignin.signOut();
      }
      // Also clear local auth tokens
      await AuthService.clearTokens();
      await AsyncStorage.removeItem("userSession");
      console.log("Google Sign-Out successful");
    } catch (error) {
      console.error("Google Sign-Out error:", error);
      throw error;
    }
  }

  // Revoke access
  static async revokeAccess() {
    try {
      if (GoogleSignin) {
        await GoogleSignin.revokeAccess();
      }
      // Also clear local auth tokens
      await AuthService.clearTokens();
      await AsyncStorage.removeItem("userSession");
      console.log("Google access revoked");
    } catch (error) {
      console.error("Google revoke access error:", error);
      throw error;
    }
  }

  // Get current user
  static async getCurrentUser() {
    try {
      if (!GoogleSignin) return null;
      const userInfo = await GoogleSignin.getCurrentUser();
      return userInfo;
    } catch (error) {
      console.error("Get current user error:", error);
      return null;
    }
  }

  // Check if user is signed in
  static async isSignedIn() {
    try {
      if (!GoogleSignin) return false;
      return await GoogleSignin.isSignedIn();
    } catch (error) {
      console.error("Check sign-in status error:", error);
      return false;
    }
  }

  // Get access token
  static async getTokens() {
    try {
      if (!GoogleSignin) throw new Error("Google Sign-In not available");
      return await GoogleSignin.getTokens();
    } catch (error) {
      console.error("Get tokens error:", error);
      throw error;
    }
  }

  // Refresh tokens if needed
  static async refreshTokensIfNeeded() {
    try {
      if (!GoogleSignin) throw new Error("Google Sign-In not available");
      const tokens = await GoogleSignin.getTokens();
      return tokens;
    } catch (error) {
      console.error("Refresh tokens error:", error);
      // If refresh fails, user needs to sign in again
      throw error;
    }
  }
}

export default GoogleAuthService;
