import AsyncStorage from "@react-native-async-storage/async-storage";

class TokenService {
  static TOKEN_KEY = "auth_token";
  static REFRESH_TOKEN_KEY = "refresh_token";

  // Token management
  static async setTokens(accessToken, refreshToken) {
    await AsyncStorage.multiSet([
      [this.TOKEN_KEY, accessToken],
      [this.REFRESH_TOKEN_KEY, refreshToken],
    ]);
  }

  static async getToken() {
    return await AsyncStorage.getItem(this.TOKEN_KEY);
  }

  static async getRefreshToken() {
    return await AsyncStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static async clearTokens() {
    await AsyncStorage.multiRemove([this.TOKEN_KEY, this.REFRESH_TOKEN_KEY, "userSession"]);
  }
}

export default TokenService;
