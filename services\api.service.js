import API_CONFIG from "../config/api.config";
import TokenService from "./token.service";

class ApiService {
  static async request(endpoint, options = {}) {
    const url = `${API_CONFIG.BASE_URL}${endpoint}`;

    const token = await TokenService.getToken();
    const defaultOptions = {
      headers: {
        "Content-Type": options?.body !== undefined && options?.body instanceof FormData ? "multipart/form-data" : "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      timeout: API_CONFIG.TIMEOUT,
    };

    const fetchOptions = {
      ...defaultOptions,
      ...options,
    };

    try {
      const response = await fetch(url, fetchOptions);

      // Handle authentication errors (401) - just clear tokens and throw
      if (response.status === 401) {
        await TokenService.clearTokens();
        throw new Error("Authentication expired");
      }

      // First check if response has content
      const text = await response.text();
      if (!text) {
        console.warn("Empty response from server");
        return null; // or return appropriate empty value
      }

      // Then try to parse as JSON
      try {
        const data = text ? JSON.parse(text) : null;
        if (!response.ok) {
          throw new Error(data?.message || `HTTP error! status: ${response.status}`);
        }
        return data;
      } catch (parseError) {
        console.error("Failed to parse response:", {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          text: text,
        });
        throw new Error(`Failed to parse response: ${parseError.message}`);
      }
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  static get(endpoint) {
    return this.request(endpoint, { method: "GET" });
  }

  static post(endpoint, data) {
    return this.request(endpoint, {
      method: "POST",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static put(endpoint, data) {
    return this.request(endpoint, {
      method: "PUT",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static delete(endpoint) {
    return this.request(endpoint, { method: "DELETE" });
  }
}

export default ApiService;
