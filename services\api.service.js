import API_CONFIG from "../config/api.config";
import TokenService from "./token.service";
// Import dynamically to avoid circular dependency
let TokenValidatorService;
let AuthService;
import("./token-validator.service").then((module) => {
  TokenValidatorService = module.default;
});
import("./auth.service").then((module) => {
  AuthService = module.default;
});

class ApiService {
  static async request(endpoint, options = {}, retryCount = 0) {
    const MAX_RETRY_COUNT = 1; // Maximum number of retry attempts

    // Prevent infinite retry loops
    if (retryCount > MAX_RETRY_COUNT) {
      throw new Error("Maximum retry count exceeded");
    }

    const url = `${API_CONFIG.BASE_URL}${endpoint}`;

    const token = await TokenService.getToken();
    const defaultOptions = {
      headers: {
        "Content-Type": options?.body !== undefined && options?.body instanceof FormData ? "multipart/form-data" : "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      timeout: API_CONFIG.TIMEOUT,
    };

    const fetchOptions = {
      ...defaultOptions,
      ...options,
    };

    try {
      const response = await fetch(url, fetchOptions);

      // Handle authentication errors (401)
      if (response.status === 401) {
        // Token expired, try to refresh
        try {
          if (!AuthService) {
            throw new Error("AuthService not loaded");
          }
          await AuthService.refreshToken();
          // Retry the original request with incremented retry count
          return await this.request(endpoint, options, retryCount + 1);
        } catch (refreshError) {
          // Refresh failed, user needs to login again
          if (AuthService) {
            await AuthService.clearTokens();
          } else {
            await TokenService.clearTokens();
          }
          throw new Error("Authentication expired");
        }
      }

      // First check if response has content
      const text = await response.text();
      if (!text) {
        console.warn("Empty response from server");
        return null; // or return appropriate empty value
      }

      // Then try to parse as JSON
      try {
        const data = text ? JSON.parse(text) : null;
        if (!response.ok) {
          throw new Error(data?.message || `HTTP error! status: ${response.status}`);
        }
        return data;
      } catch (parseError) {
        console.error("Failed to parse response:", {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          text: text,
        });
        throw new Error(`Failed to parse response: ${parseError.message}`);
      }
    } catch (error) {
      // If the error is AUTH_EXPIRED, handle it specially
      if (error.message === "AUTH_EXPIRED") {
        // Force logout if not already handled
        if (TokenValidatorService) {
          await TokenValidatorService.forceLogout();
        }
        // Note: We don't redirect here - the component will handle redirection
      }

      console.error("API request failed:", error);
      throw error;
    }
  }

  static get(endpoint) {
    return this.request(endpoint, { method: "GET" });
  }

  static post(endpoint, data) {
    return this.request(endpoint, {
      method: "POST",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static put(endpoint, data) {
    return this.request(endpoint, {
      method: "PUT",
      body: data instanceof FormData ? data : JSON.stringify(data),
    });
  }

  static delete(endpoint) {
    return this.request(endpoint, { method: "DELETE" });
  }
}

export default ApiService;
