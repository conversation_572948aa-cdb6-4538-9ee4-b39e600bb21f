{"name": "gogymium-mobile-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start -c --port 7076", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/cli-server-api": "^15.1.3", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "date-fns": "^4.1.0", "expo": "53.0.19", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "i18next": "^24.1.0", "jwt-decode": "^4.0.0", "lucide-react-native": "^0.462.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.1.4", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.0.0", "react-native-localize": "^3.3.0", "react-native-permissions": "^5.2.6", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "15.11.2", "react-native-svg-image": "^2.0.1", "react-native-svg-transformer": "^1.5.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "~29.7.0", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "~5.8.3", "expo-module-scripts": "^4.1.7"}, "private": true}