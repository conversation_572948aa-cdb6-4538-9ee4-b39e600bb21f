import React, { useEffect, useState } from "react";
import { View, Text, FlatList, TouchableOpacity, StyleSheet, TextInput, Image, Platform, Modal, ActivityIndicator, I18nManager } from "react-native";
import { useRouter } from "expo-router";
import { Search, Filter, ChevronDown, Check } from "lucide-react-native";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import MemberService from "@/services/member.service";
import { IMAGES_URL } from "@/config/api.config";
import { useTranslation } from "react-i18next";

// Status mapping:
// 1 = pending
// 2 = active
// 3 = trial
// 4 = suspended
// 5 = archived
// 6 = new

type MemberStatus = 1 | 2 | 3 | 4 | 5 | 6;

interface Member {
  id: string;
  firstName: string;
  lastName: string;
  gender: "Male" | "Female" | "Other";
  phone: string;
  createdOn: string;
  status: MemberStatus;
  avatar?: string;
}

export default function MembersScreen() {
  const router = useRouter();
  const { top } = useSafeAreaInsets();
  const { t, i18n } = useTranslation();
  const isRTL = I18nManager.isRTL;
  // Default to all statuses selected
  const [selectedStatus, setSelectedStatus] = useState<MemberStatus[]>([1, 2, 3, 4, 5, 6]);
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const [showStatusModal, setShowStatusModal] = useState(false);

  // Status options for the dropdown
  const statusOptions: { id: MemberStatus; label: string }[] = [
    { id: 1, label: t("statusBadge.pending", { fallback: "Pending" }) },
    { id: 2, label: t("statusBadge.active", { fallback: "Active" }) },
    { id: 3, label: t("statusBadge.trial", { fallback: "Trial" }) },
    { id: 4, label: t("statusBadge.suspended", { fallback: "Suspended" }) },
    { id: 5, label: t("statusBadge.archived", { fallback: "Archived" }) },
    { id: 6, label: t("statusBadge.new", { fallback: "New" }) },
  ];

  const theme = {
    background: "#fff",
    text: "#000",
    secondaryText: "#666",
    border: "#eee",
    card: "#fff",
    sectionHeader: "#000",
    categoryTag: "#007AFF",
    searchBackground: "#f5f5f5",
    cardBorder: "rgba(0, 0, 0, 0.1)",
    statusActive: {
      bg: "#E8F5E9",
      text: "#2E7D32",
    },
    statusSuspended: {
      bg: "#FFF3E0",
      text: "#EF6C00",
    },
    statusOther: {
      bg: "#E3F2FD",
      text: "#1976D2",
    },
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchMembers();
  }, []);

  // Refetch when selected status changes
  useEffect(() => {
    setHasMore(true);
    fetchMembers(1, true); // Reset to first page
  }, [selectedStatus]);

  const fetchMembers = async (page = 1, reset = false) => {
    if (loading) return;

    setLoading(true);
    try {
      // Create the status filter string from the selected statuses
      const statusFilter = selectedStatus.join(",");

      // Use a consistent page size
      const pageSize = 15;

      // Build the API URL with pagination and status filtering
      const url = `/api/Members/mymembers?pageNumber=${page}&pageSize=${pageSize}&statuses=${statusFilter}`;

      console.log(`Fetching members: ${url}`);
      const newMembers = await MemberService.getMyMembers(url);
      console.log(`Fetched ${newMembers.length} members`);

      // If we received fewer items than the page size, we've reached the end
      if (newMembers.length === 0 || newMembers.length < pageSize) {
        setHasMore(false);
      }

      // Update the members list
      setMembers((prev) => (reset ? newMembers : [...prev, ...newMembers]));
      setPageNumber(page);
    } catch (error) {
      console.error("Failed to fetch members:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchMembers(pageNumber + 1);
    }
  };

  const onRefresh = () => {
    setHasMore(true);
    fetchMembers(1, true); // Reset to first page
  };

  // Handle when the end of the list is reached
  const handleEndReached = () => {
    if (!loading && hasMore) {
      handleLoadMore();
    }
  };

  // Toggle status filter and trigger API call
  const toggleStatus = (status: MemberStatus) => {
    if (selectedStatus.includes(status)) {
      // Don't allow deselecting all statuses
      if (selectedStatus.length > 1) {
        setSelectedStatus(selectedStatus.filter((s) => s !== status));
      }
    } else {
      setSelectedStatus([...selectedStatus, status]);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    headerButtons: {
      marginTop: 24,
      marginBottom: 16,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 16,
    },
    headerText: {
      fontSize: 24,
      fontWeight: "bold",
      color: "#000",
    },
    rightButtons: {
      flexDirection: "row",
    },
    iconButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      marginLeft: 8,
    },
    searchContainer: {
      alignItems: "center",
      marginBottom: 16,
      paddingHorizontal: 12,
      height: 44,
      borderRadius: 8,
    },
    searchIcon: {
      marginRight: 8,
    },
    searchIconRTL: {
      marginLeft: 8,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,

      borderWidth: 0,
    },
    filterContainer: {},
    filterHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
    },
    filterTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: theme.secondaryText,
      marginLeft: 4,
    },
    resetButton: {
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    resetButtonText: {
      fontSize: 12,
      color: theme.categoryTag,
      fontWeight: "500",
    },
    dropdownTrigger: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.searchBackground,
      borderRadius: 8,
      marginBottom: 16,
    },
    dropdownTextContainer: {
      flex: 1,
    },
    dropdownText: {
      fontSize: 14,
      color: theme.text,
      marginBottom: 4,
    },
    selectedStatusChips: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 4,
    },
    statusChip: {
      backgroundColor: `${theme.categoryTag}20`,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 12,
      marginRight: 4,
      marginBottom: 4,
    },
    statusChipText: {
      fontSize: 12,
      color: theme.categoryTag,
      fontWeight: "500",
    },
    pickerContainer: {
      marginTop: 4,
    },
    pickerTrigger: {
      backgroundColor: theme.searchBackground,
      borderRadius: 8,
      padding: 12,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    pickerTriggerText: {
      color: theme.text,
      fontSize: 14,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: {
      backgroundColor: "#fff",
      borderRadius: 12,
      width: "90%",
      maxHeight: "80%",
      padding: 20,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: "bold",
      color: "#333",
    },
    modalCloseButton: {
      padding: 4,
    },
    modalCloseText: {
      color: theme.categoryTag,
      fontSize: 16,
    },
    optionItem: {
      padding: 16,
      justifyContent: "space-between",
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: "#eee",
    },
    optionText: {
      fontSize: 16,
      color: theme.text,
    },
    selectedOption: {
      backgroundColor: `${theme.categoryTag}10`,
    },
    selectedOptionText: {
      color: theme.categoryTag,
      fontWeight: "500",
    },
    membersContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 24,
    },
    memberCard: {
      backgroundColor: "#FFFFFF",
      borderRadius: 16,
      marginBottom: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.1)",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 2,
    },
    memberInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      marginRight: 12,
    },
    memberDetails: {
      flex: 1,
    },
    memberName: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 4,
      color: "#000000",
    },
    memberMetaData: {
      alignItems: "flex-start",
      gap: 4,
      marginBottom: 4,
    },
    gender: {
      fontSize: 14,
      marginRight: 12,
      color: "rgba(0, 0, 0, 0.6)",
    },
    phone: {
      fontSize: 14,
      color: "rgba(0, 0, 0, 0.6)",
    },
    date: {
      fontSize: 12,
      color: "rgba(0, 0, 0, 0.4)",
    },
    statusContainer: {
      marginLeft: 12,
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 20,
      fontSize: 13,
      fontWeight: "500",
      overflow: "hidden",
    },
    statusBadgeActive: {
      backgroundColor: "#E8F5E9",
    },
    statusBadgeSuspended: {
      backgroundColor: "#FFF3E0",
    },
    statusBadgeTextActive: {
      color: "#2E7D32",
    },
    statusBadgeTextSuspended: {
      color: "#EF6C00",
    },
    statusBadgeDefault: {
      backgroundColor: "#E3F2FD",
    },
  });

  //const genderOptions = ["All Genders", "Male", "Female", "Other"];

  const StatusBadge = ({ statusId }: any) => {
    const statusMap: any = {
      1: {
        label: t("statusBadge.pending"),
        badgeStyle: styles.statusBadgeDefault,
        textStyle: {},
      },
      2: {
        label: t("statusBadge.active"),
        badgeStyle: styles.statusBadgeActive,
        textStyle: styles.statusBadgeTextActive,
      },
      3: {
        label: t("statusBadge.trial"),
        badgeStyle: styles.statusBadgeDefault,
        textStyle: {},
      },
      4: {
        label: t("statusBadge.suspended"),
        badgeStyle: styles.statusBadgeSuspended,
        textStyle: styles.statusBadgeTextSuspended,
      },
      5: {
        label: t("statusBadge.archived"),
        badgeStyle: styles.statusBadgeDefault,
        textStyle: {},
      },
      6: {
        label: t("statusBadge.new"),
        badgeStyle: styles.statusBadgeDefault,
        textStyle: {},
      },
    };

    const status = statusMap[statusId] || {
      label: "Unknown",
      badgeStyle: styles.statusBadgeDefault,
      textStyle: {},
    };

    return (
      <View style={[styles.statusBadge, status.badgeStyle]}>
        <Text style={status.textStyle}>{status.label}</Text>
      </View>
    );
  };

  // Debounce search to avoid too many API calls
  const [searchQuery, setSearchQuery] = useState("");

  // Handle search input change
  const handleSearchInputChange = (text: string) => {
    setSearchQuery(text);

    // Reset pagination when search changes
    setHasMore(true);
    setPageNumber(1);

    // Fetch with the new search query
    fetchMembersWithSearch(text);
  };

  // Fetch members with search query
  const fetchMembersWithSearch = async (text: string) => {
    console.log("Searching for:", text);
    if (loading) return;

    setLoading(true);
    try {
      // Create the status filter string from the selected statuses
      const statusFilter = selectedStatus.join(",");

      // Use a consistent page size
      const pageSize = 15;

      // Build the API URL with search text and status filtering
      const url = `/api/Members/mymembers?pageNumber=1&pageSize=${pageSize}&statuses=${statusFilter}&searchText=${text}`;

      console.log(`Searching members: ${url}`);
      const newMembers = await MemberService.getMyMembers(url);
      console.log(`Found ${newMembers.length} members`);

      // If we received fewer items than the page size, we've reached the end
      if (newMembers.length === 0 || newMembers.length < pageSize) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }

      // Update the members list
      setMembers(newMembers);
      setPageNumber(1); // Reset to first page when searching
    } catch (error) {
      console.error("Failed to fetch members:", error);
    } finally {
      setLoading(false);
    }
  };

  // Render a member item
  const renderMemberItem = ({ item: member }: { item: any }) => (
    <TouchableOpacity key={member.id} style={styles.memberCard} onPress={() => router.push(`/member/${member.id}`)} activeOpacity={0.7}>
      <View style={styles.memberInfo}>
        <Image source={{ uri: `${IMAGES_URL}${member.picture}` }} style={styles.avatar} resizeMode="cover" />
        <View style={styles.memberDetails}>
          <Text style={styles.memberName}>
            {member.firstName} {member.lastName}
          </Text>
          <View style={styles.memberMetaData}>
            {member.subscriptions && member.subscriptions.length > 0 ? (
              <>
                <Text style={styles.date}>
                  {t("subscriptionStart")}:{" "}
                  {new Date(member.subscriptions[0].startDate).toLocaleDateString(i18n.language, {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </Text>
                <Text style={styles.date}>
                  {t("subscriptionEnd")}:{" "}
                  {new Date(member.subscriptions[0].endDate).toLocaleDateString(i18n.language, {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </Text>
              </>
            ) : (
              <Text style={styles.date}>{t("noSubscription", { fallback: "No active subscription" })}</Text>
            )}
          </View>
        </View>
        <View style={styles.statusContainer}>
          {member.subscriptions?.length > 0 ? (
            <StatusBadge statusId={member.subscriptions[0].subscriptionStatus.id} />
          ) : (
            <StatusBadge statusId={null} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render the list footer (loading indicator or "no more members" message)
  const renderFooter = () => {
    if (loading && pageNumber > 1) {
      return (
        <View style={{ padding: 16, alignItems: "center" }}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      );
    }

    if (!hasMore && members.length > 0) {
      return (
        <View style={{ padding: 16, alignItems: "center" }}>
          <Text style={{ color: theme.secondaryText }}>{t("noMoreMembers")}</Text>
        </View>
      );
    }

    if (members.length === 0) {
      return (
        <View style={{ padding: 16, alignItems: "center" }}>
          <Text style={{ color: theme.secondaryText }}>{t("noMembersFound")}</Text>
        </View>
      );
    }

    return null;
  };

  // Render the list header (search and filter UI)
  const renderHeader = () => (
    <View style={{ paddingTop: top }}>
      <View
        style={[
          styles.searchContainer,
          {
            backgroundColor: theme.searchBackground,
            flexDirection: isRTL ? "row-reverse" : "row",
          },
        ]}
      >
        <Search size={20} color={theme.secondaryText} style={isRTL ? styles.searchIconRTL : styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: theme.text, textAlign: isRTL ? "right" : "left" }]}
          placeholder={t("searchMembers")}
          placeholderTextColor={theme.secondaryText}
          value={searchQuery}
          clearButtonMode="while-editing"
          onChangeText={handleSearchInputChange}
          focusable
        />
      </View>

      {/* Status Filter */}
      <View style={styles.filterContainer}>
        <View style={styles.filterHeader}>
          <Text style={styles.filterTitle}>{t("filterByStatus", { fallback: "Filter by Status" })}</Text>
          <TouchableOpacity onPress={() => setSelectedStatus([1, 2, 3, 4, 5, 6])} style={styles.resetButton}>
            <Text style={styles.resetButtonText}>{t("resetFilters", { fallback: "Reset" })}</Text>
          </TouchableOpacity>
        </View>

        {/* Status Dropdown Trigger */}
        <TouchableOpacity style={styles.dropdownTrigger} onPress={() => setShowStatusModal(true)}>
          <View style={styles.dropdownTextContainer}>
            <Text style={styles.dropdownText}>
              {selectedStatus.length === 6
                ? t("allStatuses", { fallback: "All Statuses" })
                : t("selectedStatuses", {
                    count: selectedStatus.length,
                    fallback: `${selectedStatus.length} statuses selected`,
                  })}
            </Text>
            <View style={styles.selectedStatusChips}>
              {selectedStatus.length < 6 &&
                selectedStatus.map((statusId, index) => (
                  <View key={index} style={styles.statusChip}>
                    <Text style={styles.statusChipText}>{statusOptions.find((option) => option.id === statusId)?.label}</Text>
                  </View>
                ))}
            </View>
          </View>
          <ChevronDown size={20} color={theme.secondaryText} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <FlatList
        style={[styles.container, { backgroundColor: theme.background, paddingTop: top }]}
        data={members}
        renderItem={renderMemberItem}
        keyExtractor={(item, index) => item.id?.toString() || index.toString()}
        contentContainerStyle={[styles.membersContainer, { paddingBottom: 24 }]}
        showsVerticalScrollIndicator={false}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        refreshing={loading && pageNumber === 1}
        onRefresh={onRefresh}
      />

      {/* Status Selection Modal */}
      <Modal visible={showStatusModal} transparent animationType="fade" onRequestClose={() => setShowStatusModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t("selectStatus", { fallback: "Select Status" })}</Text>
              <TouchableOpacity style={styles.modalCloseButton} onPress={() => setShowStatusModal(false)}>
                <Text style={styles.modalCloseText}>{t("done", { fallback: "Done" })}</Text>
              </TouchableOpacity>
            </View>

            {statusOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.optionItem, selectedStatus.includes(option.id) && styles.selectedOption]}
                onPress={() => toggleStatus(option.id)}
              >
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Text style={[styles.optionText, selectedStatus.includes(option.id) && styles.selectedOptionText]}>{option.label}</Text>
                  {selectedStatus.includes(option.id) && (
                    <View style={{ marginLeft: "auto" }}>
                      <Check size={20} color={theme.categoryTag} />
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>
    </>
  );
}
