import ApiService from "./api.service";

interface Notification {
  id: number;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

class NotificationService {
  static async getNotifications(skip: number = 0, take: number = 10): Promise<Notification[]> {
    try {
      const response = await ApiService.get(`/api/notification?skip=${skip}&take=${take}`);
      return response;
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      throw error;
    }
  }

  static async getUnreadCount(): Promise<number> {
    try {
      const response = await ApiService.get("/api/notification/unread-count");
      return response;
    } catch (error) {
      console.error("Failed to fetch unread count:", error);
      throw error;
    }
  }

  static async markAsRead(id: number): Promise<void> {
    try {
      await ApiService.post(`/api/notification/${id}/mark-as-read`);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
      throw error;
    }
  }

  static async markAllAsRead(): Promise<void> {
    try {
      await ApiService.post("/api/notification/mark-all-read");
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
      throw error;
    }
  }

  static async deleteNotification(id: number): Promise<void> {
    try {
      await ApiService.delete(`/api/notification/${id}`);
    } catch (error) {
      console.error("Failed to delete notification:", error);
      throw error;
    }
  }
}

export default NotificationService;
