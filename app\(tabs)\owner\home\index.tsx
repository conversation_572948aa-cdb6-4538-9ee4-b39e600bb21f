import React, { useEffect, useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { <PERSON>, <PERSON>, Bell } from "lucide-react-native";
import { useTheme } from "../../../../context/ThemeContext";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ClubService from "@/services/club.service";
import FinanceService from "@/services/finance.service";
import SubscriptionService from "@/services/subscription.service";
import GroupService from "@/services/group.service";
import CoachesService from "@/services/coaches.service";
import { useTranslation } from "react-i18next";

function mapSubscriptionStatuses(subscriptions: any[]): any[] {
  // Define status names mapping
  const statusNames: Record<number, string> = {
    1: "Pending",
    2: "Active",
    3: "Trial",
    4: "Suspended",
    5: "Archived",
    6: "New",
  };

  // Count subscriptions by status
  const statusCounts = subscriptions.reduce((acc, subscription) => {
    const statusId = subscription.subscriptionStatusId;
    acc[statusId] = (acc[statusId] || 0) + 1;
    return acc;
  }, {} as Record<number, number>);

  // Create the result array
  const result = Object.entries(statusCounts).map(([statusId, count]) => ({
    statusId: Number(statusId),
    count,
    name: statusNames[Number(statusId)] || `Status ${statusId}`,
  }));

  // Include all possible statuses (even if count is 0)
  Object.entries(statusNames).forEach(([statusId, name]) => {
    const id = Number(statusId);
    if (!result.some((item) => item.statusId === id)) {
      result.push({ statusId: id, count: 0, name });
    }
  });

  return result.sort((a, b) => a.statusId - b.statusId);
}

const formatTotal = (items: any, amountField = "incomeAmount", currencyCode = "DH") => {
  if (!items || !items.length) return `0.00 ${currencyCode}`;
  const total = items.reduce((sum: any, item: any) => sum + (item[amountField] || 0), 0);
  return `${total.toFixed(2)} ${items[0].currency?.code || currencyCode}`;
};

export default function HomeScreen() {
  const { isDark } = useTheme();
  const { top } = useSafeAreaInsets();
  const { t, i18n } = useTranslation();
  const theme = {
    background: "#fff",
    text: "#000",
    secondaryText: "#666",
    border: "#eee",
    card: "#fff",
    sectionHeader: "#000",
    categoryTag: "#007AFF",
  };

  const [userRole, setUserRole] = useState<string | null>(null);
  const [userData, setUserData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true); // New state to track loading
  const [myClub, setMyClub] = useState<any | null>(null);
  const [mySubscriptonsStatus, setMySubscriptonsStatus] = useState<any | null>(null);
  const [myGroups, setMyGroups] = useState<any | null>(null);
  const [myCoaches, setMyCoaches] = useState<any | null>(null);

  const [myClubIncome, setMyClubIncome] = useState<any | null>(null);
  const [myClubExpenses, setMyClubExpenses] = useState<any | null>(null);

  const isRTL = i18n.language === "ar";

  useEffect(() => {
    console.log("Home screen mounted");
    const fetchUserRole = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userData = JSON.parse(userSession);
          const myClub = await ClubService.getMyClub();
          const myClubIncome = await FinanceService.getClubIncomes();
          const myClubExpenses = await FinanceService.getClubExpenses();
          const mySubscriptons = await SubscriptionService.getMySubscriptions();
          const myGroups = await GroupService.getMyGroups();
          const myCoaches = await CoachesService.getMyCoaches();

          setUserRole(userData.role);
          setUserData(userData);
          setMyClub(myClub);
          setMyClubIncome(myClubIncome);
          setMyClubExpenses(myClubExpenses);
          setMyGroups(myGroups);
          setMyCoaches(myCoaches);
          setMySubscriptonsStatus(mapSubscriptionStatuses(mySubscriptons));
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setIsLoading(false); // Mark loading as complete
      }
    };

    fetchUserRole();
  }, []);

  // // Only render finance and members tabs for owners
  // const isOwner = userRole === "club owner";

  // Don't render anything while loading
  if (isLoading) {
    return null; // Or a loading spinner if preferred
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 20,
    },
    headerButtons: {
      marginTop: 24,
      marginBottom: 16,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 16,
    },
    headerText: {
      fontSize: 24,
      fontWeight: "bold",
    },
    rightButtons: {
      flexDirection: "row",
    },
    iconButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      marginLeft: 8,
    },
    section: {
      paddingInline: 20,
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "700",
      marginBottom: 15,
      letterSpacing: 0.5,
    },
    cardsContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 10,
    },
    card: {
      padding: 20,
      borderRadius: 16,
      color: "#2b3481",
      backgroundColor: "#fff",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 5,
    },
    lastCard: {
      marginBottom: 0,
    },
    cardLabel: {
      fontSize: 16,
      color: "#6c757d",
      fontWeight: "500",
    },
    cardValue: {
      fontSize: 22,
      fontWeight: "700",
      letterSpacing: 0.5,
    },
    statsList: {
      backgroundColor: theme.card,
      borderRadius: 16,
      paddingInline: 16,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 5,
      marginBottom: 20,
      borderColor: theme.border,
      borderWidth: 1,
    },
    statItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.border,
    },
    statLabel: {
      fontSize: 16,
      fontWeight: "500",
    },
    statValueContainer: {
      minWidth: 48,
      height: 32,
      borderRadius: 16,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 12,
    },
    statValue: {
      fontSize: 16,
      fontWeight: "600",
    },
  });

  // Helper functions
  const getStatusBackgroundColor = (status: string, isDark: boolean) => {
    const colors = {
      Active: isDark ? "rgba(52, 199, 89, 0.15)" : "rgba(52, 199, 89, 0.1)",
      Suspended: isDark ? "rgba(255, 59, 48, 0.15)" : "rgba(255, 59, 48, 0.1)",
      Pending: isDark ? "rgba(255, 149, 0, 0.15)" : "rgba(255, 149, 0, 0.1)",
      default: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)",
    };
    return colors[status as keyof typeof colors] || colors.default;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      Active: "#34C759",
      Suspended: "#FF3B30",
      Pending: "#FF9500",
      default: "#000000",
    };
    return colors[status as keyof typeof colors] || colors.default;
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.background, paddingTop: top }]}
      showsVerticalScrollIndicator={false}
      contentInsetAdjustmentBehavior="automatic"
    >
      {/* Financial Overview Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>{t("financialOverview")}</Text>
        <View style={[styles.cardsContainer, { flexDirection: isRTL ? "row-reverse" : "row", flexWrap: "wrap", gap: 10 }]}>
          <View style={[styles.card, { flex: 1, minWidth: Dimensions.get("window").width * 0.4 }]}>
            <Text style={styles.cardLabel}>{t("totalIncome")}</Text>
            <Text style={[styles.cardValue, { fontSize: 20 }]}>{formatTotal(myClubIncome, "incomeAmount")}</Text>
          </View>

          <View style={[styles.card, { flex: 1, minWidth: Dimensions.get("window").width * 0.4 }]}>
            <Text style={styles.cardLabel}>{t("totalExpenses")}</Text>
            <Text style={[styles.cardValue, { fontSize: 20 }]}>{formatTotal(myClubExpenses, "expenseAmount")}</Text>
          </View>

          <View
            style={[
              styles.card,
              { flex: 1, minWidth: "100%", flexDirection: isRTL ? "row-reverse" : "row", justifyContent: "space-between", alignItems: "center" },
            ]}
          >
            <Text style={styles.cardLabel}>{t("profitLoss")}</Text>
            <Text style={styles.cardValue}>
              {(() => {
                const totalIncome =
                  myClubIncome?.length > 0 ? myClubIncome.reduce((sum: any, income: any) => sum + (income.incomeAmount || 0), 0) : 0;
                const totalExpenses =
                  myClubExpenses?.length > 0 ? myClubExpenses.reduce((sum: any, expense: any) => sum + (expense.expenseAmount || 0), 0) : 0;
                const currencyCode = myClubIncome?.[0]?.currency?.code || myClubExpenses?.[0]?.currency?.code || "DH";
                return `${(totalIncome - totalExpenses).toFixed(2)} ${currencyCode}`;
              })()}
            </Text>
          </View>
        </View>
      </View>

      {/* Subscription Statistics Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>{t("subscriptionStats")}</Text>
        <View style={[styles.statsList, { backgroundColor: theme.card, borderColor: theme.border }]}>
          {["New", "Active", "Suspended", "Archived", "Trial", "Pending"].map((status) => (
            <View key={status} style={[styles.statItem, { borderBottomColor: theme.border }]}>
              <Text style={[styles.statLabel, { color: theme.secondaryText }]}>{t(`statusBadge.${status.toLowerCase()}`)}</Text>
              <View
                style={[
                  styles.statValueContainer,
                  {
                    backgroundColor: getStatusBackgroundColor(status, isDark),
                  },
                ]}
              >
                <Text style={[styles.statValue, { color: getStatusColor(status) }]}>
                  {(mySubscriptonsStatus && mySubscriptonsStatus.find((s: any) => s.name === status)?.count) || 0}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Groups Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>{t("groupsOverview")}</Text>
        <View style={[styles.statsList, { backgroundColor: theme.card, borderColor: theme.border }]}>
          {myGroups?.map((group: any, index: number) => (
            <View key={index} style={[styles.statItem, { borderBottomColor: theme.border }]}>
              <Text style={[styles.statLabel, { color: theme.secondaryText }]}>{group.name}</Text>
              <View style={[styles.statValueContainer, { backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)" }]}>
                <Text style={[styles.statValue, { color: theme.text }]}>{group.membersCount}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Coaches Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>{t("coachesOverview")}</Text>
        <View style={[styles.statsList, { backgroundColor: theme.card, borderColor: theme.border }]}>
          {myCoaches?.map((coach: any, index: number) => (
            <View key={index} style={[styles.statItem, { borderBottomColor: theme.border }]}>
              <Text style={[styles.statLabel, { color: theme.secondaryText }]}>{coach.name}</Text>
              <View style={[styles.statValueContainer, { backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)" }]}>
                <Text style={[styles.statValue, { color: theme.text }]}>{coach.groupCount}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
}
