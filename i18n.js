import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import * as Localization from "expo-localization";
import { I18nManager } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

import en from "./locals/en.json";
import fr from "./locals/fr.json";
import ar from "./locals/ar.json";
const resources = {
  en: { translation: en },
  fr: { translation: fr },
  ar: { translation: ar },
};

// Define which languages are RTL
const RTL_LANGUAGES = ["ar"];

const languageDetector = {
  type: "languageDetector",
  async: true,
  detect: async (callback) => {
    try {
      // Check if we're in a web environment
      if (typeof window === "undefined") {
        // We're in a server-side rendering environment, use default language
        callback("en");
        return;
      }

      // Try to get saved language from AsyncStorage first
      const savedLanguage = await AsyncStorage.getItem("user-language");

      if (savedLanguage) {
        // Set RTL layout if the language is RTL
        const isRTL = RTL_LANGUAGES.includes(savedLanguage);
        if (I18nManager.isRTL !== isRTL) {
          I18nManager.forceRTL(isRTL);
        }
        callback(savedLanguage);
        return;
      }
    } catch (error) {
      console.error("Error reading language from storage:", error);
    }

    // Fall back to device locale if no saved language
    const locale = Localization.getLocales()[0]?.languageCode || "en";

    // Set RTL layout if the language is RTL
    const isRTL = RTL_LANGUAGES.includes(locale);
    if (I18nManager.isRTL !== isRTL) {
      I18nManager.forceRTL(isRTL);
    }

    callback(locale);
  },
  init: () => {},
  cacheUserLanguage: async (language) => {
    try {
      // Check if we're in a web environment
      if (typeof window === "undefined") {
        // We're in a server-side rendering environment, skip saving
        return;
      }

      // Save selected language to AsyncStorage
      await AsyncStorage.setItem("user-language", language);

      // Set RTL layout if the language is RTL
      const isRTL = RTL_LANGUAGES.includes(language);
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.forceRTL(isRTL);
      }
    } catch (error) {
      console.error("Error saving language to storage:", error);
    }
  },
};

i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "en",
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;
