import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Tabs } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Camera, Crosshair, DollarSign, QrCode, User } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Platform } from "react-native";

export default function TabLayout() {
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true); // New state to track loading
  const { t } = useTranslation();

  useEffect(() => {
    console.log("User tab layout mounted");
    const fetchUserRole = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userData = JSON.parse(userSession);
          setUserRole(userData.role);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setIsLoading(false); // Mark loading as complete
      }
    };

    fetchUserRole();

    // Validate session when entering protected routes
    //TokenValidatorService.validateSession();
  }, []);

  // Only render finance and members tabs for owners
  const isOwner = userRole === "owner";

  // Don't render anything while loading
  if (isLoading) {
    return null; // Or a loading spinner if preferred
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors["light"].tabIconSelected,
        tabBarInactiveTintColor: Colors["light"].tabIconDefault,
        headerShown: false,
        //tabBarBackground: TabBarBackground,
        tabBarStyle: {
          backgroundColor: Colors["light"].background,
          borderTopWidth: 1,
          height: Platform.OS === "ios" ? 88 : 52,
        },
      }}
    >
      <Tabs.Screen
        name="clubs"
        options={{
          title: t("clubs"),
          tabBarIcon: ({ color }) => <Crosshair color={color} size={20} />,
        }}
      />
      <Tabs.Screen
        name="subscription"
        options={{
          title: t("subscription"),
          tabBarIcon: ({ color }) => <DollarSign color={color} size={20} />,
        }}
      />
      {/* <Tabs.Screen
        name="workout"
        options={{
          title: t("workout"),
          tabBarIcon: ({ color }) => <BicepsFlexed color={color} size={20} />,
        }}
      /> */}
      <Tabs.Screen
        name="profile"
        options={{
          title: t("profile"),
          tabBarIcon: ({ color }) => <User color={color} size={20} />,
        }}
      />
      <StatusBar style="auto" />
    </Tabs>
  );
}
