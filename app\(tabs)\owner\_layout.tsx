import { Tabs } from "expo-router";
import React, { useEffect, useState } from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Camera, DollarSign, Home, QrCode, User, Users } from "lucide-react-native";
import { useTranslation } from "react-i18next";

export default function TabLayout() {
  const { t } = useTranslation();
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true); // New state to track loading

  useEffect(() => {
    console.log("Owner tab layout mounted");
    const fetchUserRole = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userData = JSON.parse(userSession);
          setUserRole(userData.role);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setIsLoading(false); // Mark loading as complete
      }
    };

    fetchUserRole();
  }, []);

  // Don't render anything while loading
  if (isLoading) {
    return null; // Or a loading spinner if preferred
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors["light"].tabIconSelected,
        tabBarInactiveTintColor: Colors["light"].tabIconDefault,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          backgroundColor: Colors["light"].background,
          borderTopColor: "lightgray",
          borderTopWidth: 1,
          height: Platform.OS === "ios" ? 88 : 52,
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t("myClub"),
          tabBarIcon: ({ color }) => <Home color={color} size={20} />,
        }}
      />
      {/* 
      <Tabs.Screen
        name="finance"
        options={{
          title: "Finance",
          tabBarIcon: ({ color }) => <DollarSign color={color} size={20} />,
        }}
      /> */}

      <Tabs.Screen
        name="members"
        options={{
          title: t("members"),
          tabBarIcon: ({ color }) => <Users color={color} size={20} />,
        }}
      />

      <Tabs.Screen
        name="upload-image"
        options={{
          title: t("camera"),
          tabBarIcon: ({ color }) => <Camera color={color} size={20} />,
        }}
      />

      <Tabs.Screen
        name="qr-code-scanner"
        options={{
          title: t("qrcodeScanner"),
          tabBarIcon: ({ color }) => <QrCode color={color} size={20} />,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: t("profile"),
          tabBarIcon: ({ color }) => <User color={color} size={20} />,
        }}
      />
    </Tabs>
  );
}
