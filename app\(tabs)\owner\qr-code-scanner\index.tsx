import React, { useState, useEffect, useRef } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert, Dimensions, ActivityIndicator } from "react-native";
import { useTranslation } from "react-i18next";
import { CameraView, useCameraPermissions } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import MemberService from "@/services/member.service";
import { IMAGES_URL } from "@/config/api.config";

const { width } = Dimensions.get("window");
const scannerSize = width * 0.8;

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export default function QrCodeScannerScreen() {
  const { t } = useTranslation();
  const [facing, setFacing] = useState<"front" | "back">("back");
  const [scanned, setScanned] = useState(false);
  const [memberData, setMemberData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const cameraRef = useRef<CameraView>(null);
  const [permission, requestPermission] = useCameraPermissions();

  useEffect(() => {
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission]);

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (!data || scanned) return;

    setScanned(true);
    setLoading(true);
    setError(null);

    try {
      // // Validate QR code format (example: 15046-7413)
      // if (!/^\d{5}-\d{4}$/.test(data) || !/^\d{4}-\d{5}$/.test(data)) {
      //   throw new Error(t("invalidQrCode"));
      // }

      const response = await MemberService.findByQrCode(data);

      if (response) {
        setMemberData(response);
      } else {
        setError(t("scanFailed"));
      }
      setLoading(false);
    } catch (err: any) {
      console.error("Failed to fetch member data:", err);
      setError(t("scanFailed"));
      // setTimeout(() => {
      //   setScanned(false);
      // }, 2000);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const resetScanner = () => {
    setScanned(false);
    setMemberData(null);
    setError(null);
  };

  const toggleCameraFacing = () => {
    setFacing((current) => (current === "back" ? "front" : "back"));
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>{t("cameraPermissionDenied")}</Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>{t("grantPermission")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.subtitle}>{t("positionQrCodeInFrame")}</Text>

      {!scanned && (
        <View style={styles.scannerContainer}>
          <CameraView
            ref={cameraRef}
            style={styles.scanner}
            facing={facing}
            barcodeScannerSettings={{
              barcodeTypes: ["qr"],
            }}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          >
            <View style={styles.scannerOverlay}>
              <View style={styles.scannerFrame} />
              <TouchableOpacity style={styles.flipButton} onPress={toggleCameraFacing}>
                <Ionicons name="camera-reverse" size={28} color="white" />
              </TouchableOpacity>
            </View>
          </CameraView>
        </View>
      )}

      {memberData && (
        <View style={styles.memberInfoContainer}>
          {/* Member Header with Image and Basic Info */}
          <View style={styles.memberHeader}>
            {memberData.picture && <Image source={{ uri: `${IMAGES_URL}${memberData.picture}` }} style={styles.memberImage} />}
            <View style={styles.memberNameContainer}>
              <Text style={styles.memberName}>
                {memberData.firstName} {memberData.lastName}
              </Text>
            </View>
          </View>

          {/* Subscription Information */}
          <View style={styles.infoSection}>
            {memberData.subscriptions && memberData.subscriptions.length > 0 ? (
              memberData.subscriptions.map((subscription: any, index: number) => (
                <View key={index} style={styles.subscriptionItem}>
                  <Text style={styles.sectionTitle}>{t("subscriptionInfo")}</Text>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{t("status")}:</Text>
                    <Text
                      style={[styles.detailValue, subscription.subscriptionStatus.label === "Active" ? styles.activeStatus : styles.inactiveStatus]}
                    >
                      {subscription.subscriptionStatus.label}
                    </Text>
                  </View>
                  {subscription.group && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>{t("group")}:</Text>
                      <Text style={styles.detailValue}>{subscription.group.name}</Text>
                    </View>
                  )}
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{t("startDate")}:</Text>
                    <Text style={styles.detailValue}>{subscription.startDate ? formatDate(subscription.startDate) : "-"}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>{t("endDate")}:</Text>
                    <Text style={styles.detailValue}>{subscription.endDate ? formatDate(subscription.endDate) : "-"}</Text>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.noSubscription}>
                <Text style={styles.inactiveStatus}>{t("noSubscription")}</Text>
                <Text style={styles.subtitle}>{t("notSubscribedToGroups")}</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {loading && (
        <View style={[styles.resultProcess, styles.scannerPlaceholder]}>
          <ActivityIndicator size="large" color="#000" />
        </View>
      )}

      {error && (
        <View style={[styles.resultProcess, styles.scannerPlaceholder]}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      {error && !memberData && (
        <TouchableOpacity style={styles.scanAgainButton} onPress={resetScanner}>
          <Ionicons name="scan-outline" size={20} style={styles.scanAgainIcon} />
          <Text style={styles.scanAgainText}>{t("tryAgain")}</Text>
        </TouchableOpacity>
      )}

      {!loading && !error && memberData && (
        <TouchableOpacity style={styles.scanAgainButton} onPress={resetScanner}>
          <Ionicons name="scan-outline" size={20} style={styles.scanAgainIcon} />
          <Text style={styles.scanAgainText}>{t("scanAgain")}</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    backgroundColor: "#fff",
  },
  permissionText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: "#007AFF",
    padding: 15,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  scannerContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 30,
    position: "relative",
  },
  scanner: {
    width: scannerSize,
    height: scannerSize,
  },
  resultProcess: {
    width: scannerSize,
    height: scannerSize,
    marginInline: "auto",
    alignItems: "center",
    justifyContent: "center",
  },
  scannerOverlay: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  scannerFrame: {
    width: scannerSize - 40,
    height: scannerSize - 40,
    borderWidth: 2,
    borderColor: "rgba(0, 255, 0, 0.5)",
    borderRadius: 10,
  },
  flipButton: {
    position: "absolute",
    bottom: 20,
    right: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 50,
    padding: 10,
  },
  scannerPlaceholder: {
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
  },
  errorText: {
    color: "red",
    fontSize: 16,
    textAlign: "center",
    padding: 20,
  },
  memberInfoContainer: {
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    margin: 16,
    boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
    elevation: 3,
  },
  memberHeader: {
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: 16,
    marginBottom: 16,
  },
  memberImage: {
    width: 200,
    height: 200,
    borderRadius: 100,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: "gray",
  },
  memberNameContainer: {
    flex: 1,
  },
  memberName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  memberGender: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  memberId: {
    fontSize: 12,
    color: "#999",
    marginTop: 2,
  },
  infoSection: {
    paddingBottom: 0,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: "#333",
    flex: 1,
    textAlign: "right",
  },
  activeStatus: {
    color: "#2ecc71",
    fontWeight: "bold",
  },
  inactiveStatus: {
    color: "#e74c3c",
    fontWeight: "bold",
  },
  subscriptionItem: {
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 6,
    marginBottom: 6,
  },
  noSubscription: {
    backgroundColor: "#f9f9f9",
    padding: 16,
    borderRadius: 6,
    alignItems: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#999",
    marginTop: 4,
    textAlign: "center",
    marginBottom: 20,
  },
  scanAgainButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#3498db",
    padding: 12,
    borderRadius: 6,
    marginTop: 16,
  },
  scanAgainIcon: {
    color: "#fff",
    marginRight: 8,
  },
  scanAgainText: {
    color: "#fff",
    fontWeight: "bold",
  },
});
