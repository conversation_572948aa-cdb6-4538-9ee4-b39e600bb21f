import ApiService from "./api.service";
import { jwtDecode } from "jwt-decode";
import TokenService from "./token.service";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface LoginCredentials {
  username: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

interface MemberRegisterData extends RegisterData {
  clubId: string;
}

interface FacebookAuthData {
  accessToken: string;
}

interface PasswordChange {
  currentPassword: string;
  newPassword: string;
}

interface PasswordReset {
  email: string;
  token: string;
  newPassword: string;
}

class AuthService {
  // Token management methods now delegate to TokenService
  static async setTokens(accessToken: string, refreshToken: string) {
    return TokenService.setTokens(accessToken, refreshToken);
  }

  static async getToken(): Promise<string | null> {
    return TokenService.getToken();
  }

  static async getRefreshToken(): Promise<string | null> {
    return TokenService.getRefreshToken();
  }

  static async clearTokens() {
    return TokenService.clearTokens();
  }

  // Authentication endpoints
  static async login(credentials: LoginCredentials) {
    try {
      const response = await ApiService.post("/api/Account/login", credentials);
      if (response.accessToken && response.refreshToken) {
        await this.setTokens(response.accessToken, response.refreshToken);
      }
      return response;
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }

  static async register(data: RegisterData) {
    try {
      return await ApiService.post("/api/Account/register", data);
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    }
  }

  static async memberRegister(data: MemberRegisterData) {
    try {
      return await ApiService.post("/api/Account/member/register", data);
    } catch (error) {
      console.error("Member registration failed:", error);
      throw error;
    }
  }

  // Facebook authentication
  static async registerWithFacebook(data: FacebookAuthData) {
    try {
      return await ApiService.post("/api/Account/register/facebook", data);
    } catch (error) {
      console.error("Facebook registration failed:", error);
      throw error;
    }
  }

  static async authenticateWithFacebook(data: FacebookAuthData) {
    try {
      const response = await ApiService.post("/api/Account/authenticate/facebook", data);
      if (response.token && response.refreshToken) {
        await this.setTokens(response.token, response.refreshToken);
        // Map token to accessToken for consistency with the login page
        response.accessToken = response.token;
      }
      return response;
    } catch (error) {
      console.error("Facebook authentication failed:", error);
      throw error;
    }
  }

  // User profile endpoints
  static async getMemberProfile() {
    try {
      return await ApiService.get("/api/Account/members/me");
    } catch (error) {
      console.error("Failed to fetch member profile:", error);
      throw error;
    }
  }

  static async getClubOwnerProfile() {
    try {
      return await ApiService.get("/api/Account/clubowner/me");
    } catch (error) {
      console.error("Failed to fetch club owner profile:", error);
      throw error;
    }
  }

  static async getAdminProfile() {
    try {
      return await ApiService.get("/api/Account/admin/me");
    } catch (error) {
      console.error("Failed to fetch admin profile:", error);
      throw error;
    }
  }

  static async updateProfile(data: Partial<RegisterData>) {
    try {
      return await ApiService.put("/api/Account/me", data);
    } catch (error) {
      console.error("Profile update failed:", error);
      throw error;
    }
  }

  // Password and email management
  static async changePassword(data: PasswordChange) {
    try {
      return await ApiService.put("/api/Account/change-password", data);
    } catch (error) {
      console.error("Password change failed:", error);
      throw error;
    }
  }

  static async validateEmail(email: string) {
    try {
      return await ApiService.put(`/api/Account/validate-email/${email}`);
    } catch (error) {
      console.error("Email validation failed:", error);
      throw error;
    }
  }

  static async checkEmailValidation() {
    try {
      return await ApiService.get("/api/Account/me/is-email-validated");
    } catch (error) {
      console.error("Email validation check failed:", error);
      throw error;
    }
  }

  static async resetPassword(data: PasswordReset) {
    try {
      return await ApiService.put("/api/Account/reset-password", data);
    } catch (error) {
      console.error("Password reset failed:", error);
      throw error;
    }
  }

  // Role testing
  static async testRole() {
    try {
      return await ApiService.get("/api/Account/test-role");
    } catch (error) {
      console.error("Role test failed:", error);
      throw error;
    }
  }

  static decodeUserRole(token: string): string {
    try {
      const decodedToken: any = jwtDecode(token);
      return decodedToken["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"] || "user";
    } catch (error) {
      console.error("Error decoding token:", error);
      return "user";
    }
  }

  static decodeUser(token: string) {
    const decodedToken = jwtDecode(token);
    return decodedToken;
  }

  // Google authentication - updated to handle both old and new formats
  static async authenticateWithGoogle(data: { code?: string; idToken?: string; accessToken?: string }) {
    try {
      const response = await ApiService.post("/api/Account/authenticate/google", data);
      if (response.accessToken && response.refreshToken) {
        await this.setTokens(response.accessToken, response.refreshToken);
      }
      return response;
    } catch (error) {
      console.error("Google authentication failed:", error);
      throw error;
    }
  }

  // Simple token validation - no API calls, just local checks
  static async isAuthenticated(): Promise<boolean> {
    try {
      const token = await this.getToken();

      // If no token exists, return false (don't clear storage here)
      if (!token) {
        return false;
      }

      // Check if token is expired based on its payload
      try {
        const decodedToken: any = jwtDecode(token);
        const currentTime = Date.now() / 1000;

        // If token is expired, return false (don't clear storage here)
        if (!decodedToken.exp || decodedToken.exp <= currentTime) {
          return false;
        }

        return true;
      } catch (decodeError) {
        // If token can't be decoded, it's invalid
        console.error("Invalid token format:", decodeError);
        return false;
      }
    } catch (error) {
      console.error("Error checking authentication:", error);
      return false;
    }
  }

  // Method to clear all authentication data - call this when you want to logout
  static async logout(): Promise<void> {
    try {
      await this.clearTokens();
      console.log("User logged out successfully");
    } catch (error) {
      console.error("Error during logout:", error);
    }
  }
}

export default AuthService;
