import React, { useEffect, useState } from "react";
import { ActivityIndicator, Alert } from "react-native";
import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useTheme } from "@/context/ThemeContext";
import { StatusBar } from "expo-status-bar";
import { TouchableOpacity, View, Text, Modal, StyleSheet, Switch, ScrollView } from "react-native";
import { Bell, MapPin, Search, Settings, Trash2, Check } from "lucide-react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ClubService from "@/services/club.service";
import NotificationService from "@/services/notification.service";
import { useTranslation } from "react-i18next";
import { format, formatDistance } from "date-fns";

interface ClubSetting {
  id: number;
  settingKey: string;
  settingValue: string;
  entityType: string;
  entityId: number;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
}

interface Notification {
  id: number;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

interface NotificationModalProps {
  visible: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (id: number) => void;
  onMarkAllAsRead: () => void;
  onDelete: (id: number) => void;
  isLoading: boolean;
}

const NotificationModal = ({ visible, onClose, notifications, onMarkAsRead, onMarkAllAsRead, onDelete, isLoading }: NotificationModalProps) => {
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <Modal visible={visible} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <ActivityIndicator size="large" color="#4A69FF" />
            <Text style={styles.loadingText}>{t("loadingNotifications")}</Text>
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>{t("notifications")}</Text>
            <Text style={styles.subtitle}>{t("recentNotifications")}</Text>
          </View>

          {notifications && notifications.length === 0 ? (
            <View style={styles.emptyNotifications}>
              <Text style={styles.emptyNotificationsText}>{t("noNotifications")}</Text>
            </View>
          ) : (
            <>
              <ScrollView style={styles.notificationsList}>
                {notifications?.map((notification) => (
                  <View key={notification.id} style={[styles.notificationItem, !notification.isRead && styles.unreadNotification]}>
                    <View style={styles.notificationContent}>
                      <Text style={styles.notificationTitle}>{notification.title}</Text>
                      <Text style={styles.notificationMessage}>{notification.message}</Text>
                      <Text style={styles.notificationTime}>{formatDistance(new Date(notification.createdAt), new Date(), { addSuffix: true })}</Text>
                    </View>
                    <View style={styles.notificationActions}>
                      {!notification.isRead && (
                        <TouchableOpacity style={styles.notificationAction} onPress={() => onMarkAsRead(notification.id)}>
                          <Check size={18} color="#4A69FF" />
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity style={styles.notificationAction} onPress={() => onDelete(notification.id)}>
                        <Trash2 size={18} color="#FF4A4A" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                  <Text style={styles.cancelButtonText}>{t("close")}</Text>
                </TouchableOpacity>
                {notifications?.some((n) => !n.isRead) && (
                  <TouchableOpacity style={styles.saveButton} onPress={onMarkAllAsRead}>
                    <Text style={styles.saveButtonText}>{t("markAllAsRead")}</Text>
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const SettingsModal = ({ visible, onClose }: SettingsModalProps) => {
  const [clubSettings, setClubSettings] = useState<ClubSetting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { t } = useTranslation();

  // Initialize settings state based on API data
  const [settings, setSettings] = useState({
    trialMemberships: false,
    notifyMembers: false,
    newSubscriptionAlerts: false,
    expiredSubscriptionAlerts: false,
    showClubToPublic: false,
  });

  useEffect(() => {
    const fetchClubSettings = async () => {
      try {
        setIsLoading(true);
        const settingsData = await ClubService.getMyClubSettings();
        if (settingsData) {
          setClubSettings(settingsData);

          // Map API settings to local state
          const mappedSettings = {
            trialMemberships: settingsData.find((s: any) => s.settingKey === "AllowTrialMembership")?.settingValue === "True",
            notifyMembers: settingsData.find((s: any) => s.settingKey === "NotifyMembers")?.settingValue === "True",
            newSubscriptionAlerts: settingsData.find((s: any) => s.settingKey === "GetNotifiedNewSubscription")?.settingValue === "True",
            expiredSubscriptionAlerts: settingsData.find((s: any) => s.settingKey === "GetNotifiedExpiredSubscription")?.settingValue === "True",
            showClubToPublic: settingsData.find((s: any) => s.settingKey === "ShowMyClubToPublic")?.settingValue === "True",
          };

          setSettings(mappedSettings);
        }
      } catch (error) {
        console.error("Error fetching club settings:", error);
        Alert.alert(t("error"), t("failedToLoadSettings"));
      } finally {
        setIsLoading(false);
      }
    };

    if (visible) {
      fetchClubSettings();
    }
  }, [visible]);

  const handleSaveChanges = async () => {
    setIsSaving(true);

    // Prepare updated settings
    if (clubSettings) {
      const updatedSettings = [
        {
          id: clubSettings.find((s) => s.settingKey === "AllowTrialMembership")?.id,
          settingKey: "AllowTrialMembership",
          settingValue: settings.trialMemberships ? "True" : "False",
          entityType: "Club",
          entityId: clubSettings[0]?.entityId, // Assuming all have same entityId
        },
        {
          id: clubSettings.find((s) => s.settingKey === "NotifyMembers")?.id,
          settingKey: "NotifyMembers",
          settingValue: settings.notifyMembers ? "True" : "False",
          entityType: "Club",
          entityId: clubSettings[0]?.entityId,
        },
        {
          id: clubSettings.find((s) => s.settingKey === "GetNotifiedNewSubscription")?.id,
          settingKey: "GetNotifiedNewSubscription",
          settingValue: settings.newSubscriptionAlerts ? "True" : "False",
          entityType: "Club",
          entityId: clubSettings[0]?.entityId,
        },
        {
          id: clubSettings.find((s) => s.settingKey === "GetNotifiedExpiredSubscription")?.id,
          settingKey: "GetNotifiedExpiredSubscription",
          settingValue: settings.expiredSubscriptionAlerts ? "True" : "False",
          entityType: "Club",
          entityId: clubSettings[0]?.entityId,
        },
        {
          id: clubSettings.find((s) => s.settingKey === "ShowMyClubToPublic")?.id,
          settingKey: "ShowMyClubToPublic",
          settingValue: settings.showClubToPublic ? "True" : "False",
          entityType: "Club",
          entityId: clubSettings[0]?.entityId,
        },
      ];

      // Call API to update settings
      await ClubService.updateClubSettings(updatedSettings);
    }

    Alert.alert(t("success"), t("settingsSavedSuccessfully"));
    onClose();

    setIsSaving(false);
  };

  if (isLoading) {
    return (
      <Modal visible={visible} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <ActivityIndicator size="large" color="#4A69FF" />
            <Text style={styles.loadingText}>{t("loadingSettings")}</Text>
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>{t("clubSettings")}</Text>
            <Text style={styles.subtitle}>{t("manageClubPreferences")}</Text>
          </View>

          <ScrollView style={styles.settingsList}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>{t("trialMemberships")}</Text>
                <Text style={styles.settingDescription}>{t("trialMembershipsDescription")}</Text>
              </View>
              <Switch
                value={settings.trialMemberships}
                onValueChange={(value) => setSettings((prev) => ({ ...prev, trialMemberships: value }))}
                trackColor={{ false: "#767577", true: "#4A69FF" }}
                thumbColor="#f4f3f4"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>{t("memberNotifications")}</Text>
                <Text style={styles.settingDescription}>{t("memberNotificationsDescription")}</Text>
              </View>
              <Switch
                value={settings.notifyMembers}
                onValueChange={(value) => setSettings((prev) => ({ ...prev, notifyMembers: value }))}
                trackColor={{ false: "#767577", true: "#4A69FF" }}
                thumbColor="#f4f3f4"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>{t("newSubscriptionAlerts")}</Text>
                <Text style={styles.settingDescription}>{t("newSubscriptionAlertsDescription")}</Text>
              </View>
              <Switch
                value={settings.newSubscriptionAlerts}
                onValueChange={(value) => setSettings((prev) => ({ ...prev, newSubscriptionAlerts: value }))}
                trackColor={{ false: "#767577", true: "#4A69FF" }}
                thumbColor="#f4f3f4"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>{t("expiredSubscriptionAlerts")}</Text>
                <Text style={styles.settingDescription}>{t("expiredSubscriptionAlertsDescription")}</Text>
              </View>
              <Switch
                value={settings.expiredSubscriptionAlerts}
                onValueChange={(value) => setSettings((prev) => ({ ...prev, expiredSubscriptionAlerts: value }))}
                trackColor={{ false: "#767577", true: "#4A69FF" }}
                thumbColor="#f4f3f4"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>{t("showClubToPublic")}</Text>
                <Text style={styles.settingDescription}>{t("showClubToPublicDescription")}</Text>
              </View>
              <Switch
                value={settings.showClubToPublic}
                onValueChange={(value) => setSettings((prev) => ({ ...prev, showClubToPublic: value }))}
                trackColor={{ false: "#767577", true: "#4A69FF" }}
                thumbColor="#f4f3f4"
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose} disabled={isSaving}>
              <Text style={styles.cancelButtonText}>{t("cancel")}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.saveButton, isSaving && styles.saveButtonDisabled]} onPress={handleSaveChanges} disabled={isSaving}>
              {isSaving ? <ActivityIndicator color="#fff" /> : <Text style={styles.saveButtonText}>{t("saveChanges")}</Text>}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const Layout = () => {
  const { isDark } = useTheme();
  const router = useRouter();
  const { t } = useTranslation();
  const [isSettingsModalVisible, setIsSettingsModalVisible] = useState(false);
  const [isNotificationModalVisible, setIsNotificationModalVisible] = useState(false);
  const backgroundColor = Colors.light.background;
  const [isLoading, setIsLoading] = useState(true);
  const [isNotificationsLoading, setIsNotificationsLoading] = useState(true);
  const [myClub, setMyClub] = useState<any | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch club data
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userData = JSON.parse(userSession);
          const myClub = await ClubService.getMyClub();

          setMyClub(myClub);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setIsLoading(false); // Mark loading as complete
      }
    };

    fetchUserRole();
  }, []);

  // Fetch notifications and unread count
  const fetchNotifications = async () => {
    try {
      setIsNotificationsLoading(true);
      const notificationsData = await NotificationService.getNotifications(0, 20);

      setNotifications(notificationsData);

      const count = await NotificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      Alert.alert(t("error"), t("failedToLoadNotifications"));
    } finally {
      setIsNotificationsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();

    const intervalId = setInterval(async () => {
      try {
        const count = await NotificationService.getUnreadCount();
        setUnreadCount(count);
      } catch (error) {
        console.error("Error fetching unread count:", error);
      }
    }, 5000);

    return () => clearInterval(intervalId);
  }, []);

  // Refresh notifications when modal is opened
  useEffect(() => {
    if (isNotificationModalVisible) {
      fetchNotifications();
    }
  }, [isNotificationModalVisible]);

  // Handle marking a notification as read
  const handleMarkAsRead = async (id: number) => {
    try {
      await NotificationService.markAsRead(id);

      // Update local state
      setNotifications(notifications.map((notification) => (notification.id === id ? { ...notification, isRead: true } : notification)));

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
      Alert.alert(t("error"), t("failedToMarkAsRead"));
    }
  };

  // Handle marking all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await NotificationService.markAllAsRead();

      // Update local state
      setNotifications(notifications.map((notification) => ({ ...notification, isRead: true })));

      // Update unread count
      setUnreadCount(0);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      Alert.alert(t("error"), t("failedToMarkAllAsRead"));
    }
  };

  // Handle deleting a notification
  const handleDeleteNotification = async (id: number) => {
    try {
      await NotificationService.deleteNotification(id);

      // Update local state
      const updatedNotifications = notifications.filter((notification) => notification.id !== id);
      setNotifications(updatedNotifications);

      // Update unread count if needed
      const deletedNotification = notifications.find((n) => n.id === id);
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Error deleting notification:", error);
      Alert.alert(t("error"), t("failedToDeleteNotification"));
    }
  };

  return (
    <>
      <Stack
        screenOptions={{
          headerShadowVisible: false,
          contentStyle: { backgroundColor: backgroundColor },
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: myClub?.name || "My Club",
            headerLargeTitle: true,
            headerRight: () => (
              <View style={styles.headerRight}>
                <TouchableOpacity style={styles.iconButton} onPress={() => setIsSettingsModalVisible(!isSettingsModalVisible)}>
                  <Settings color="#fff" size={24} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.iconButton} onPress={() => setIsNotificationModalVisible(true)}>
                  <Bell color="#fff" size={24} />
                  {unreadCount > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{unreadCount > 99 ? "99+" : unreadCount}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            ),
          }}
        />

        <StatusBar style="auto" />
      </Stack>
      <SettingsModal visible={isSettingsModalVisible} onClose={() => setIsSettingsModalVisible(false)} />
      <NotificationModal
        visible={isNotificationModalVisible}
        onClose={() => setIsNotificationModalVisible(false)}
        notifications={notifications}
        onMarkAsRead={handleMarkAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
        onDelete={handleDeleteNotification}
        isLoading={isNotificationsLoading}
      />
    </>
  );
};

const styles = StyleSheet.create({
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    alignItems: "center",
    justifyContent: "center",
    color: "#fff",
    position: "relative",
  },
  headerRight: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    justifyContent: "center",
    paddingRight: 16,
  },
  badge: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#FF4A4A",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 4,
    borderWidth: 1,
    borderColor: "#fff",
  },
  badgeText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 12,
    width: "90%",
    maxHeight: "80%",
    padding: 20,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  settingsList: {},
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  settingDescription: {
    fontSize: 13,
    color: "#666",
    marginTop: 4,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 16,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
  },
  cancelButtonText: {
    color: "#666",
    fontWeight: "600",
  },
  saveButton: {
    backgroundColor: "#4A69FF",
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 10,
    minWidth: 120,
    alignItems: "center",
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: "#fff",
    fontWeight: "600",
  },
  loadingText: {
    marginTop: 16,
    color: "#666",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#000",
  },
  headerSubtitle: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
    maxWidth: 300,
  },
  // Notification styles
  notificationsList: {
    maxHeight: 500,
  },
  notificationItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  unreadNotification: {
    backgroundColor: "rgba(74, 105, 255, 0.05)",
  },
  notificationContent: {
    flex: 1,
    marginRight: 10,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  notificationMessage: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: "#999",
    marginTop: 4,
  },
  notificationActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  notificationAction: {
    padding: 8,
    marginLeft: 4,
  },
  emptyNotifications: {
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyNotificationsText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});

export default Layout;
