import { jwtDecode } from "jwt-decode";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";

// Import AuthService dynamically to avoid circular dependency
let AuthService: any = null;
const loadAuthService = async () => {
  if (!AuthService) {
    const authModule = await import("./auth.service");
    AuthService = authModule.default;
  }
  return AuthService;
};

class TokenValidatorService {
  // Check if token is valid (not expired and properly formatted)
  static isTokenValid(token: any) {
    if (!token) return false;

    try {
      const decodedToken = jwtDecode(token);
      const currentTime = Date.now() / 1000;

      // Check if token has expiration and is not expired
      return decodedToken.exp && decodedToken.exp > currentTime;
    } catch (error) {
      console.error("Error validating token:", error);
      return false;
    }
  }

  // Check token on app start and during navigation
  static async validateSession() {
    try {
      const authService = await loadAuthService();
      const token = await authService.getToken();

      if (!token || !this.isTokenValid(token)) {
        // Try to refresh the token
        try {
          await authService.refreshToken();
          // Check if the new token is valid
          const newToken = await authService.getToken();
          if (!newToken || !this.isTokenValid(newToken)) {
            await this.forceLogout();
            return false;
          }
          return true;
        } catch (error) {
          // Refresh failed, force logout
          await this.forceLogout();
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error("Error during session validation:", error);
      await this.forceLogout();
      return false;
    }
  }

  // Force logout without direct router usage
  static async forceLogout(showSessionExpiredMessage = true) {
    try {
      console.log("Forcing logout due to expired session");

      // Load AuthService dynamically
      const authService = await loadAuthService();

      // Clear tokens
      await authService.clearTokens();

      // Clear user session
      await AsyncStorage.removeItem("userSession");

      // Store session expired flag if needed
      if (showSessionExpiredMessage) {
        await AsyncStorage.setItem("session_expired", "true");
      }

      // We no longer redirect here - the component that calls this will handle redirection
      return true;
    } catch (error) {
      console.error("Error during force logout:", error);
      return false;
    }
  }

  // Set up periodic token validation
  static setupPeriodicValidation(intervalMinutes = 5) {
    // Check token validity every X minutes
    const intervalId = setInterval(async () => {
      await this.validateSession();
    }, intervalMinutes * 60 * 1000);

    return intervalId; // Return interval ID so it can be cleared if needed
  }
}

export default TokenValidatorService;
